"""
注册器模块 - 替代 mmcv.utils.Registry
使用纯 Python 实现注册器模式
"""
from typing import Any, Callable, Dict, Optional, Type, Union


class Registry:
    """注册器类，替代 mmcv.utils.Registry"""
    
    def __init__(self, name: str, build_func: Optional[Callable] = None, parent: Optional['Registry'] = None):
        """
        初始化注册器
        
        Args:
            name: 注册器名称
            build_func: 构建函数
            parent: 父注册器
        """
        self._name = name
        self._module_dict: Dict[str, Any] = {}
        self._children: Dict[str, 'Registry'] = {}
        self._build_func = build_func
        self._parent = parent
        
        if parent is not None:
            parent._children[name] = self
    
    @property
    def name(self) -> str:
        """获取注册器名称"""
        return self._name
    
    @property
    def module_dict(self) -> Dict[str, Any]:
        """获取模块字典"""
        return self._module_dict
    
    def get(self, key: str) -> Any:
        """获取注册的模块"""
        if key in self._module_dict:
            return self._module_dict[key]
        
        # 在父注册器中查找
        if self._parent is not None:
            return self._parent.get(key)
        
        raise KeyError(f"'{key}' 未在注册器 '{self._name}' 中注册")
    
    def register_module(self, name: Optional[str] = None, force: bool = False, module: Optional[Type] = None) -> Union[Type, Callable]:
        """
        注册模块
        
        Args:
            name: 模块名称，如果为 None 则使用类名
            force: 是否强制覆盖已存在的模块
            module: 要注册的模块类
        
        Returns:
            装饰器函数或注册的模块
        """
        if module is not None:
            # 直接注册模块
            self._register_module(module, name, force)
            return module
        
        # 返回装饰器
        def decorator(cls: Type) -> Type:
            self._register_module(cls, name, force)
            return cls
        
        return decorator
    
    def _register_module(self, module: Type, name: Optional[str] = None, force: bool = False):
        """内部注册方法"""
        if name is None:
            name = module.__name__
        
        if not force and name in self._module_dict:
            raise KeyError(f"模块 '{name}' 已在注册器 '{self._name}' 中注册")
        
        self._module_dict[name] = module
    
    def build(self, cfg: Dict[str, Any], *args, **kwargs) -> Any:
        """
        构建对象
        
        Args:
            cfg: 配置字典，必须包含 'type' 字段
            *args: 位置参数
            **kwargs: 关键字参数
        
        Returns:
            构建的对象实例
        """
        if not isinstance(cfg, dict):
            raise TypeError(f"配置必须是字典类型，得到 {type(cfg)}")
        
        if 'type' not in cfg:
            raise KeyError("配置字典必须包含 'type' 字段")
        
        cfg = cfg.copy()
        obj_type = cfg.pop('type')
        
        if isinstance(obj_type, str):
            obj_cls = self.get(obj_type)
        elif isinstance(obj_type, type):
            obj_cls = obj_type
        else:
            raise TypeError(f"type 必须是字符串或类型，得到 {type(obj_type)}")
        
        # 使用自定义构建函数或默认构造函数
        if self._build_func is not None:
            return self._build_func(cfg, registry=self, *args, **kwargs)
        else:
            return obj_cls(*args, **cfg, **kwargs)
    
    def __contains__(self, key: str) -> bool:
        """检查模块是否已注册"""
        return key in self._module_dict or (self._parent is not None and key in self._parent)
    
    def __len__(self) -> int:
        """获取注册模块数量"""
        return len(self._module_dict)
    
    def __iter__(self):
        """迭代注册的模块名称"""
        return iter(self._module_dict.keys())
    
    def keys(self):
        """获取所有注册的模块名称"""
        return self._module_dict.keys()
    
    def values(self):
        """获取所有注册的模块"""
        return self._module_dict.values()
    
    def items(self):
        """获取所有注册的模块名称和模块对"""
        return self._module_dict.items()
    
    def __repr__(self) -> str:
        return f"Registry(name='{self._name}', items={list(self._module_dict.keys())})"


def build_from_cfg(cfg: Dict[str, Any], registry: Registry, default_args: Optional[Dict[str, Any]] = None) -> Any:
    """
    从配置构建对象
    
    Args:
        cfg: 配置字典
        registry: 注册器
        default_args: 默认参数
    
    Returns:
        构建的对象实例
    """
    if default_args is not None:
        cfg = {**default_args, **cfg}
    
    return registry.build(cfg)
