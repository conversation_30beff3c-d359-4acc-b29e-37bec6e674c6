"""
文件工具模块 - 替代 mmcv 中的文件操作函数
使用 Python 标准库实现文件和目录操作功能
"""
import os
import shutil
import tempfile
from pathlib import Path
from typing import Optional, Union


def mkdir_or_exist(dir_name: Union[str, Path], mode: int = 0o777) -> None:
    """
    创建目录（如果不存在），替代 mmcv.mkdir_or_exist
    
    Args:
        dir_name: 目录路径
        mode: 目录权限模式
    """
    if dir_name == '':
        return
    
    dir_name = Path(dir_name)
    if not dir_name.exists():
        dir_name.mkdir(parents=True, exist_ok=True, mode=mode)


def check_file_exist(filename: Union[str, Path], msg_tmpl: str = 'file "{}" does not exist') -> None:
    """
    检查文件是否存在，如果不存在则抛出异常
    
    Args:
        filename: 文件路径
        msg_tmpl: 错误消息模板
    
    Raises:
        FileNotFoundError: 当文件不存在时
    """
    if not Path(filename).exists():
        raise FileNotFoundError(msg_tmpl.format(filename))


def scandir(dir_path: Union[str, Path], 
           suffix: Optional[Union[str, tuple]] = None,
           recursive: bool = False,
           case_sensitive: bool = True) -> list:
    """
    扫描目录中的文件
    
    Args:
        dir_path: 目录路径
        suffix: 文件后缀过滤器
        recursive: 是否递归扫描子目录
        case_sensitive: 是否区分大小写
    
    Returns:
        文件路径列表
    """
    dir_path = Path(dir_path)
    if not dir_path.is_dir():
        raise NotADirectoryError(f"'{dir_path}' 不是一个目录")
    
    files = []
    
    if recursive:
        pattern = '**/*'
        iterator = dir_path.rglob(pattern)
    else:
        iterator = dir_path.iterdir()
    
    for item in iterator:
        if item.is_file():
            if suffix is not None:
                if isinstance(suffix, str):
                    suffix = (suffix,)
                
                item_suffix = item.suffix
                if not case_sensitive:
                    item_suffix = item_suffix.lower()
                    suffix = tuple(s.lower() for s in suffix)
                
                if item_suffix in suffix:
                    files.append(str(item))
            else:
                files.append(str(item))
    
    return sorted(files)


def find_vcs_root(path: Union[str, Path], markers: tuple = ('.git', '.hg', '.svn')) -> Optional[str]:
    """
    查找版本控制系统根目录
    
    Args:
        path: 起始路径
        markers: 版本控制标记文件/目录
    
    Returns:
        版本控制根目录路径，如果未找到则返回 None
    """
    path = Path(path).resolve()
    
    for parent in [path] + list(path.parents):
        for marker in markers:
            if (parent / marker).exists():
                return str(parent)
    
    return None


def symlink(src: Union[str, Path], 
           dst: Union[str, Path], 
           overwrite: bool = True,
           **kwargs) -> None:
    """
    创建符号链接
    
    Args:
        src: 源文件/目录路径
        dst: 目标链接路径
        overwrite: 是否覆盖已存在的链接
        **kwargs: 其他参数
    """
    src = Path(src)
    dst = Path(dst)
    
    if dst.exists() or dst.is_symlink():
        if overwrite:
            if dst.is_dir() and not dst.is_symlink():
                shutil.rmtree(dst)
            else:
                dst.unlink()
        else:
            raise FileExistsError(f"目标路径已存在: {dst}")
    
    # 确保目标目录存在
    dst.parent.mkdir(parents=True, exist_ok=True)
    
    # 创建符号链接
    dst.symlink_to(src)


def copy_file(src: Union[str, Path], 
             dst: Union[str, Path],
             follow_symlinks: bool = True) -> None:
    """
    复制文件
    
    Args:
        src: 源文件路径
        dst: 目标文件路径
        follow_symlinks: 是否跟随符号链接
    """
    src = Path(src)
    dst = Path(dst)
    
    # 确保目标目录存在
    dst.parent.mkdir(parents=True, exist_ok=True)
    
    if follow_symlinks:
        shutil.copy2(src, dst)
    else:
        shutil.copy2(src, dst, follow_symlinks=False)


def move_file(src: Union[str, Path], dst: Union[str, Path]) -> None:
    """
    移动文件
    
    Args:
        src: 源文件路径
        dst: 目标文件路径
    """
    src = Path(src)
    dst = Path(dst)
    
    # 确保目标目录存在
    dst.parent.mkdir(parents=True, exist_ok=True)
    
    shutil.move(str(src), str(dst))


def remove_file(file_path: Union[str, Path]) -> None:
    """
    删除文件
    
    Args:
        file_path: 文件路径
    """
    file_path = Path(file_path)
    if file_path.exists():
        file_path.unlink()


def remove_dir(dir_path: Union[str, Path]) -> None:
    """
    删除目录
    
    Args:
        dir_path: 目录路径
    """
    dir_path = Path(dir_path)
    if dir_path.exists() and dir_path.is_dir():
        shutil.rmtree(dir_path)


def get_temp_dir() -> str:
    """
    获取临时目录路径
    
    Returns:
        临时目录路径
    """
    return tempfile.gettempdir()


def is_filepath(x: Union[str, Path]) -> bool:
    """
    检查是否为文件路径

    Args:
        x: 待检查的对象

    Returns:
        是否为文件路径
    """
    return isinstance(x, (str, Path))


class Timer:
    """计时器类，替代 mmcv.Timer"""

    def __init__(self):
        import time
        self.start_time = time.time()

    def since_start(self) -> float:
        """获取从开始到现在的时间（秒）"""
        import time
        return time.time() - self.start_time

    def reset(self):
        """重置计时器"""
        import time
        self.start_time = time.time()
