"""
This is a tool to preprocess the original giant remote sensing .TIF images.
"""
import gdal
import cv2
import argparse
import sys
sys.path.append('.')
from utils import mkdir_or_exist
from datasets.utils import save_image


def parse_args():
    parser = argparse.ArgumentParser(description='handle raw .TIF images')
    parser.add_argument('-d', '--data_dir', required=True, help='data directory')
    parser.add_argument('-s', '--satellite', required=True, help='satellite name')
    parser.add_argument('-n', '--n', type=int, default=2, help='total image id to handle')
    parser.add_argument('--bit_depth', type=int, default=10, help='bit depth')
    return parser.parse_args()


def handle_raw(in_dir, out_dir, n, bit_depth):
    """处理原始 TIF 图像"""
    for num in range(n):
        # 处理多光谱图像
        mul_path = f'{in_dir}/{num}_mul.tif'
        if os.path.exists(mul_path):
            dt_mul = gdal.Open(mul_path)
            img_mul = dt_mul.ReadAsArray()
            save_image(f'{out_dir}/{num}_mul.tif', img_mul, bit_depth)
        
        # 处理全色图像
        pan_path = f'{in_dir}/{num}_pan.tif'
        if os.path.exists(pan_path):
            dt_pan = gdal.Open(pan_path)
            img_pan = dt_pan.ReadAsArray()
            save_image(f'{out_dir}/{num}_pan.tif', img_pan, bit_depth)
        
        # 处理低分辨率图像
        lr_path = f'{in_dir}/{num}_lr.tif'
        if os.path.exists(lr_path):
            dt_lr = gdal.Open(lr_path)
            img_lr = dt_lr.ReadAsArray()
            save_image(f'{out_dir}/{num}_lr.tif', img_lr, bit_depth)


if __name__ == "__main__":
    args = parse_args()
    in_dir = f'{args.data_dir}/Raw/{args.satellite}'
    out_dir = f'{args.data_dir}/Dataset/{args.satellite}'
    mkdir_or_exist(out_dir)

    with open(f'{out_dir}/handle_raw.log', 'w') as f:
        for k, v in args._get_kwargs():
            f.write(f'{k} = {v}\n')
    
    handle_raw(in_dir, out_dir, args.n, args.bit_depth)
