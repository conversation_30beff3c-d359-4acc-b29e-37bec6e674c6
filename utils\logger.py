"""
日志模块 - 替代 mmcv.utils.get_logger
使用 Python 标准库 logging 模块实现日志功能
"""
import os
import sys
import logging
from typing import Optional, Union


def get_logger(name: str, log_file: Optional[str] = None, log_level: Union[str, int] = logging.INFO) -> logging.Logger:
    """
    获取日志记录器，替代 mmcv.utils.get_logger
    
    Args:
        name: 日志记录器名称
        log_file: 日志文件路径，如果为 None 则只输出到控制台
        log_level: 日志级别，可以是字符串或整数
    
    Returns:
        配置好的日志记录器
    """
    # 转换日志级别
    if isinstance(log_level, str):
        log_level = getattr(logging, log_level.upper())
    
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(log_level)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 创建格式化器
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 添加控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 添加文件处理器（如果指定了日志文件）
    if log_file is not None:
        # 确保日志文件目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def setup_logger(name: str, 
                 log_file: Optional[str] = None, 
                 log_level: Union[str, int] = logging.INFO,
                 console_output: bool = True,
                 file_mode: str = 'a') -> logging.Logger:
    """
    设置日志记录器的高级版本
    
    Args:
        name: 日志记录器名称
        log_file: 日志文件路径
        log_level: 日志级别
        console_output: 是否输出到控制台
        file_mode: 文件写入模式 ('a' 追加, 'w' 覆盖)
    
    Returns:
        配置好的日志记录器
    """
    # 转换日志级别
    if isinstance(log_level, str):
        log_level = getattr(logging, log_level.upper())
    
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(log_level)
    
    # 清除现有处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 创建格式化器
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 添加控制台处理器
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # 添加文件处理器
    if log_file is not None:
        # 确保日志文件目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, mode=file_mode, encoding='utf-8')
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


class LoggerContext:
    """日志上下文管理器"""
    
    def __init__(self, logger: logging.Logger, level: Union[str, int]):
        self.logger = logger
        self.original_level = logger.level
        if isinstance(level, str):
            level = getattr(logging, level.upper())
        self.temp_level = level
    
    def __enter__(self):
        self.logger.setLevel(self.temp_level)
        return self.logger
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.logger.setLevel(self.original_level)


def get_root_logger(log_file: Optional[str] = None, log_level: Union[str, int] = logging.INFO) -> logging.Logger:
    """
    获取根日志记录器
    
    Args:
        log_file: 日志文件路径
        log_level: 日志级别
    
    Returns:
        根日志记录器
    """
    return get_logger('root', log_file, log_level)


def print_log(msg: str, logger: Optional[logging.Logger] = None, level: Union[str, int] = logging.INFO):
    """
    打印日志消息
    
    Args:
        msg: 日志消息
        logger: 日志记录器，如果为 None 则打印到控制台
        level: 日志级别
    """
    if logger is None:
        print(msg)
    else:
        if isinstance(level, str):
            level = getattr(logging, level.upper())
        logger.log(level, msg)
