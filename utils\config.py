"""
配置文件管理模块 - 替代 mmcv.Config
使用 Python 标准库实现配置文件加载和管理功能
"""
import os
import sys
import copy
import types
from typing import Any, Dict, Union


class Config:
    """配置类，替代 mmcv.Config"""
    
    def __init__(self, cfg_dict: Dict[str, Any] = None, cfg_text: str = None, filename: str = None):
        """
        初始化配置对象
        
        Args:
            cfg_dict: 配置字典
            cfg_text: 配置文本内容
            filename: 配置文件路径
        """
        if cfg_dict is not None:
            self._cfg_dict = cfg_dict
        elif cfg_text is not None:
            self._cfg_dict = self._parse_config_text(cfg_text)
        elif filename is not None:
            self._cfg_dict = self._load_from_file(filename)
        else:
            self._cfg_dict = {}
        
        self._filename = filename
        self._text = cfg_text
        
        # 将字典内容设置为对象属性
        self._dict_to_config_dict(self._cfg_dict)
    
    def _load_from_file(self, filename: str) -> Dict[str, Any]:
        """从文件加载配置"""
        if not os.path.exists(filename):
            raise FileNotFoundError(f"配置文件不存在: {filename}")
        
        if filename.endswith('.py'):
            return self._load_python_config(filename)
        else:
            raise ValueError(f"不支持的配置文件格式: {filename}")
    
    def _load_python_config(self, filename: str) -> Dict[str, Any]:
        """加载 Python 配置文件"""
        # 获取文件的绝对路径和目录
        abs_filename = os.path.abspath(filename)
        config_dir = os.path.dirname(abs_filename)
        
        # 临时添加配置文件目录到 Python 路径
        if config_dir not in sys.path:
            sys.path.insert(0, config_dir)
        
        try:
            # 动态导入配置模块
            spec = self._import_config_module(abs_filename)
            config_dict = {}
            
            # 提取模块中的配置变量
            for key, value in spec.__dict__.items():
                if not key.startswith('_'):  # 忽略私有变量
                    config_dict[key] = value
            
            return config_dict
        finally:
            # 清理 Python 路径
            if config_dir in sys.path:
                sys.path.remove(config_dir)
    
    def _import_config_module(self, filename: str):
        """动态导入配置模块"""
        module_name = os.path.splitext(os.path.basename(filename))[0]
        spec = types.ModuleType(module_name)
        
        with open(filename, 'r', encoding='utf-8') as f:
            code = compile(f.read(), filename, 'exec')
            exec(code, spec.__dict__)
        
        return spec
    
    def _parse_config_text(self, text: str) -> Dict[str, Any]:
        """解析配置文本"""
        config_dict = {}
        exec(text, config_dict)
        # 移除内置变量
        return {k: v for k, v in config_dict.items() if not k.startswith('__')}
    
    def _dict_to_config_dict(self, d: Dict[str, Any]):
        """将字典转换为配置对象属性"""
        for key, value in d.items():
            if isinstance(value, dict):
                # 递归处理嵌套字典
                nested_config = Config(value)
                setattr(self, key, nested_config)
            else:
                setattr(self, key, value)
    
    @classmethod
    def fromfile(cls, filename: str) -> 'Config':
        """从文件创建配置对象"""
        return cls(filename=filename)
    
    def copy(self) -> 'Config':
        """创建配置的深拷贝"""
        return Config(copy.deepcopy(self._cfg_dict))
    
    def __getitem__(self, key: str) -> Any:
        """支持字典式访问"""
        return getattr(self, key)
    
    def __setitem__(self, key: str, value: Any):
        """支持字典式设置"""
        setattr(self, key, value)
        self._cfg_dict[key] = value
    
    def __contains__(self, key: str) -> bool:
        """支持 in 操作符"""
        return hasattr(self, key)
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，支持默认值"""
        return getattr(self, key, default)
    
    def pop(self, key: str, default: Any = None) -> Any:
        """弹出配置值"""
        value = getattr(self, key, default)
        if hasattr(self, key):
            delattr(self, key)
            if key in self._cfg_dict:
                del self._cfg_dict[key]
        return value
    
    def update(self, other: Union[Dict[str, Any], 'Config']):
        """更新配置"""
        if isinstance(other, Config):
            other_dict = other._cfg_dict
        else:
            other_dict = other
        
        self._cfg_dict.update(other_dict)
        self._dict_to_config_dict(other_dict)
    
    @property
    def pretty_text(self) -> str:
        """格式化的配置文本"""
        return self._format_dict(self._cfg_dict)
    
    def _format_dict(self, d: Dict[str, Any], indent: int = 0) -> str:
        """格式化字典为可读文本"""
        lines = []
        prefix = "  " * indent
        
        for key, value in d.items():
            if isinstance(value, dict):
                lines.append(f"{prefix}{key}:")
                lines.append(self._format_dict(value, indent + 1))
            else:
                lines.append(f"{prefix}{key}: {value}")
        
        return "\n".join(lines)
    
    def __repr__(self) -> str:
        return f"Config({self._cfg_dict})"
    
    def __str__(self) -> str:
        return self.pretty_text
